#!/usr/bin/env python3
"""
Minimap Viewer Demo Application

Demonstrates the minimap viewer functionality with all features:
- Zoom in/out capabilities
- Floor navigation with proper ordering
- Camera position preservation across floor changes
- Interactive controls and status information
"""

import sys
import os
from pathlib import Path
import logging

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QTextEdit, QSplitter, QGroupBox, QGridLayout,
    QMessageBox, QFrame
)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont

from minimap_viewer import MinimapViewer, MinimapViewerWindow

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class MinimapDemo(QMainWindow):
    """
    Demo application showcasing the minimap viewer capabilities.
    """
    
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("FiendishFinder - Minimap Viewer Demo")
        self.setGeometry(50, 50, 1400, 900)
        
        self.setup_ui()
        self.setup_demo_features()
    
    def setup_ui(self):
        """Set up the demo user interface."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Create splitter for resizable panels
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel - Minimap viewer
        self.minimap_viewer = MinimapViewer()
        self.minimap_viewer.floorChanged.connect(self.on_floor_changed)
        
        # Right panel - Demo controls and information
        right_panel = self.create_demo_panel()
        
        # Add to splitter
        splitter.addWidget(self.minimap_viewer)
        splitter.addWidget(right_panel)
        
        # Set splitter proportions (70% minimap, 30% demo panel)
        splitter.setSizes([1000, 400])
        
        main_layout.addWidget(splitter)
        
        # Status bar
        self.status_bar = self.statusBar()
        self.update_status()
        
        # Timer for periodic status updates
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # Update every second
    
    def create_demo_panel(self) -> QWidget:
        """Create the demo control panel."""
        panel = QWidget()
        panel.setMaximumWidth(400)
        panel.setMinimumWidth(300)
        
        layout = QVBoxLayout(panel)
        
        # Title
        title = QLabel("Minimap Viewer Demo")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Features section
        features_group = QGroupBox("Features Demonstrated")
        features_layout = QVBoxLayout(features_group)
        
        features_text = QTextEdit()
        features_text.setReadOnly(True)
        features_text.setMaximumHeight(150)
        features_text.setPlainText(
            "✓ Zoom functionality (mouse wheel + Ctrl, buttons, slider)\n"
            "✓ Floor navigation with proper ordering\n"
            "✓ Camera position preservation across floors\n"
            "✓ Interactive pan and zoom controls\n"
            "✓ Real-time status updates\n"
            "✓ Smooth zoom transitions\n"
            "✓ Fit-to-view functionality"
        )
        features_layout.addWidget(features_text)
        layout.addWidget(features_group)
        
        # Quick actions section
        actions_group = QGroupBox("Quick Actions")
        actions_layout = QGridLayout(actions_group)
        
        # Floor navigation buttons
        self.floor_up_btn = QPushButton("Floor Up")
        self.floor_down_btn = QPushButton("Floor Down")
        self.main_floor_btn = QPushButton("Main Floor (07)")
        
        self.floor_up_btn.clicked.connect(self.go_floor_up)
        self.floor_down_btn.clicked.connect(self.go_floor_down)
        self.main_floor_btn.clicked.connect(self.go_main_floor)
        
        actions_layout.addWidget(self.floor_up_btn, 0, 0)
        actions_layout.addWidget(self.floor_down_btn, 0, 1)
        actions_layout.addWidget(self.main_floor_btn, 1, 0, 1, 2)
        
        # Zoom preset buttons
        zoom_25_btn = QPushButton("25% Zoom")
        zoom_100_btn = QPushButton("100% Zoom")
        zoom_200_btn = QPushButton("200% Zoom")
        
        zoom_25_btn.clicked.connect(lambda: self.set_zoom(0.25))
        zoom_100_btn.clicked.connect(lambda: self.set_zoom(1.0))
        zoom_200_btn.clicked.connect(lambda: self.set_zoom(2.0))
        
        actions_layout.addWidget(zoom_25_btn, 2, 0)
        actions_layout.addWidget(zoom_100_btn, 2, 1)
        actions_layout.addWidget(zoom_200_btn, 3, 0, 1, 2)
        
        layout.addWidget(actions_group)
        
        # Information section
        info_group = QGroupBox("Current Information")
        info_layout = QVBoxLayout(info_group)
        
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        self.info_text.setMaximumHeight(200)
        info_layout.addWidget(self.info_text)
        
        layout.addWidget(info_group)
        
        # Instructions section
        instructions_group = QGroupBox("Controls")
        instructions_layout = QVBoxLayout(instructions_group)
        
        instructions_text = QTextEdit()
        instructions_text.setReadOnly(True)
        instructions_text.setMaximumHeight(120)
        instructions_text.setPlainText(
            "• Mouse wheel + Ctrl: Zoom in/out\n"
            "• Middle mouse button: Pan around\n"
            "• Floor dropdown: Change floors\n"
            "• Zoom slider: Precise zoom control\n"
            "• Buttons: Quick actions"
        )
        instructions_layout.addWidget(instructions_text)
        layout.addWidget(instructions_group)
        
        layout.addStretch()
        
        return panel
    
    def setup_demo_features(self):
        """Set up demo-specific features."""
        # Check if minimap images are available
        available_floors = self.minimap_viewer.get_available_floors()
        if not available_floors:
            QMessageBox.warning(
                self,
                "No Minimap Images",
                "No minimap images found in the 'processed_minimap' directory.\n"
                "Please run the minimap stitcher first to generate floor images."
            )
        else:
            logger.info(f"Demo loaded with {len(available_floors)} floors: {available_floors}")
    
    def on_floor_changed(self, floor: int):
        """Handle floor change events."""
        self.update_info_display()
        logger.info(f"Demo: Floor changed to {floor}")
    
    def go_floor_up(self):
        """Navigate to the floor above current."""
        current_floor = self.minimap_viewer.get_current_floor()
        available_floors = sorted(self.minimap_viewer.get_available_floors())
        
        try:
            current_index = available_floors.index(current_floor)
            if current_index > 0:
                new_floor = available_floors[current_index - 1]
                self.minimap_viewer.set_floor(new_floor)
        except ValueError:
            pass
    
    def go_floor_down(self):
        """Navigate to the floor below current."""
        current_floor = self.minimap_viewer.get_current_floor()
        available_floors = sorted(self.minimap_viewer.get_available_floors())
        
        try:
            current_index = available_floors.index(current_floor)
            if current_index < len(available_floors) - 1:
                new_floor = available_floors[current_index + 1]
                self.minimap_viewer.set_floor(new_floor)
        except ValueError:
            pass
    
    def go_main_floor(self):
        """Navigate to the main floor (floor 07)."""
        if 7 in self.minimap_viewer.get_available_floors():
            self.minimap_viewer.set_floor(7)
    
    def set_zoom(self, zoom_factor: float):
        """Set zoom to a specific factor."""
        self.minimap_viewer.graphics_view.zoom_to_factor(zoom_factor)
    
    def update_status(self):
        """Update status bar."""
        camera_info = self.minimap_viewer.get_camera_info()
        available_floors = len(self.minimap_viewer.get_available_floors())
        
        status_text = (
            f"Floor: {camera_info['floor']:02d} | "
            f"Zoom: {camera_info['zoom_factor']:.1f}x | "
            f"Position: ({camera_info['center_x']:.0f}, {camera_info['center_y']:.0f}) | "
            f"Available Floors: {available_floors}"
        )
        
        self.status_bar.showMessage(status_text)
        self.update_info_display()
    
    def update_info_display(self):
        """Update the information display."""
        camera_info = self.minimap_viewer.get_camera_info()
        available_floors = self.minimap_viewer.get_available_floors()
        
        info_text = f"""Current Floor: {camera_info['floor']:02d}
Zoom Factor: {camera_info['zoom_factor']:.2f}x
Center Position: ({camera_info['center_x']:.0f}, {camera_info['center_y']:.0f})

Available Floors: {len(available_floors)}
Floor List: {', '.join(f'{f:02d}' for f in sorted(available_floors))}

Camera Position Preservation: Active
- Position and zoom are maintained when switching floors
- Use floor navigation to test this feature
"""
        
        self.info_text.setPlainText(info_text)


def main():
    """Main function to run the demo application."""
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("FiendishFinder Minimap Viewer Demo")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("FiendishFinder")
    
    # Create and show demo window
    demo = MinimapDemo()
    demo.show()
    
    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
