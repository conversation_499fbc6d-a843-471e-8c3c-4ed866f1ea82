#!/usr/bin/env python3
"""
FiendishFinder - Tibia Minimap Stitcher

Simple example usage of the minimap stitching system.
"""

from minimap_stitcher import MinimapStitchingSystem

def main():
    """Main function demonstrating the minimap stitching system."""
    print("FiendishFinder - Tibia Minimap Stitcher")
    print("=" * 50)

    # Initialize the stitching system
    system = MinimapStitchingSystem("raw_minimap", "processed_minimap")

    # Get available floors
    floors = system.get_available_floors()
    print(f"Found {len(floors)} floors: {floors}")

    # Process all floors
    print("\nProcessing all floors...")
    summary = system.process_all_floors('PNG')

    # Print results
    successful = sum(1 for floor_data in summary.values() if floor_data['success'])
    total = len(summary)

    print(f"\nResults: {successful}/{total} floors processed successfully")

    for floor in sorted(summary.keys()):
        floor_data = summary[floor]
        status = "✓" if floor_data['success'] else "✗"
        print(f"  {status} Floor {floor:2d}: {floor_data['tile_count']:2d} tiles -> "
              f"{floor_data['dimensions']['width_pixels']}x{floor_data['dimensions']['height_pixels']} pixels")

    # Save summary report
    report_path = system.save_summary_report(summary)
    print(f"\nDetailed report saved to: {report_path}")

if __name__ == "__main__":
    main()